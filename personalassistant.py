import sys
import os
import json
import io
import threading
import ctypes
import pyautogui
import keyboard
import pytesseract
import cv2
import numpy as np
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QMessageBox, QComboBox, QLineEdit, QDialog,
                             QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QPainter, QPixmap, QImage, QFont
from ai_services import AIService

# Windows API constants
GWL_EXSTYLE = -20
WS_EX_TOOLWINDOW = 0x00000080
WS_EX_APPWINDOW = 0x00040000
WS_EX_LAYERED = 0x00080000
WS_EX_NOACTIVATE = 0x08000000

DEBUG_MODE = True
SYSTEM_PROMPT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_prompt.txt")
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")

default_config = {
    'gemini_api_key': '',
    'mistral_api_key': '',
    'active_service': 'gemini',
    'mistral_model': 'mistral-small',
    'openrouter_api_key': '',
    'ollama_models': []
}

def load_config():
    config = default_config.copy()
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            print(f"Error loading config: {e}")
    return config

def save_config(config):
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
    except Exception as e:
        print(f"Error saving config: {e}")

pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def load_system_prompt():
    # Updated system prompt for image interpretation and question answering
    return (
        "You are an AI assistant. Interpret the provided image and its OCR text. "
        "If there is a question present in the image, answer it directly. "
        "Otherwise, provide a helpful summary or interpretation."
        "If the image contains a multiple choice question, "
        "analyze the options and provide the answer first then the explanation. "
    )

class TaskbarControlPanel(QWidget):
    """Transparent taskbar-style control panel"""
    model_changed = pyqtSignal(str)

    def __init__(self, config, ai_service):
        super().__init__()
        self.config = config
        self.ai_service = ai_service
        self.init_ui()
        self.apply_stealth_window_style()

    def init_ui(self):
        # Set window properties for taskbar-style appearance
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)

        # Condensed taskbar - adaptive to screen size
        screen = QApplication.primaryScreen().geometry()
        taskbar_width = min(600, screen.width() - 100)  # Adaptive width, max 600px
        taskbar_height = 50

        # Position in top-left with some margin, but ensure it fits on screen
        x = min(50, screen.width() - taskbar_width - 20)
        y = min(20, screen.height() - taskbar_height - 20)

        self.setGeometry(x, y, taskbar_width, taskbar_height)

        # Create main layout
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 8, 15, 8)
        layout.setSpacing(12)

        # App title/logo with icon - centered
        title_label = QLabel("◉ Lotus")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #fff;
            font-size: 13pt;
            font-weight: bold;
            background: rgba(0,0,0,0.8);
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid rgba(255,255,255,0.2);
        """)
        layout.addWidget(title_label)

        # Model selector
        self.model_combo = QComboBox()
        self._populate_models()
        self.model_combo.setCurrentText(self._get_display_model())
        self.model_combo.setStyleSheet("""
            QComboBox {
                background: rgba(0,0,0,0.8);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 10pt;
                min-width: 80px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #fff;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background: rgba(0,0,0,0.9);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                selection-background-color: rgba(255,255,255,0.2);
            }
        """)
        self.model_combo.currentTextChanged.connect(self._on_model_changed)
        layout.addWidget(self.model_combo)

        # Controls info
        controls_label = QLabel("Alt+C: Screenshot | Ctrl+←→: Move | Ctrl+↑↓: Scroll | Ctrl+\\: Hide")
        controls_label.setStyleSheet("""
            color: #aaa;
            font-size: 9pt;
            background: rgba(0,0,0,0.7);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(255,255,255,0.1);
        """)
        layout.addWidget(controls_label)

        # Settings button with icon
        self.settings_btn = QPushButton("⚙ Settings")
        self.settings_btn.setStyleSheet("""
            QPushButton {
                background: rgba(0,0,0,0.8);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 6px 10px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.1);
            }
        """)
        # Don't connect here - will be connected from main window
        layout.addWidget(self.settings_btn)

        # Refresh models button
        self.refresh_btn = QPushButton("↻")
        self.refresh_btn.setToolTip("Refresh Ollama models")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: rgba(0,0,0,0.8);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 6px 8px;
                font-size: 12pt;
                max-width: 30px;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.1);
            }
        """)
        layout.addWidget(self.refresh_btn)

        self.setLayout(layout)

    def _populate_models(self):
        """Populate the model dropdown with available models"""
        self.model_combo.clear()

        # Add standard cloud models
        self.model_combo.addItems(["Gemini", "Mistral", "DeepSeek"])

        # Add Ollama models if available
        ollama_models = self.ai_service.get_ollama_models()

        if ollama_models:
            self.model_combo.addItem("─── Local Models ───")  # Separator
            for model in ollama_models:
                self.model_combo.addItem(f"Ollama: {model}")

    def _get_display_model(self):
        service = self.config.get('active_service', 'gemini')
        if service == 'gemini':
            return 'Gemini'
        elif service == 'mistral':
            return 'Mistral'
        elif service == 'deepseek':
            return 'DeepSeek'
        elif service.startswith('ollama:'):
            model_name = service.replace('ollama:', '')
            return f'Ollama: {model_name}'
        return 'Gemini'

    def _on_model_changed(self, model_text):
        # Skip separator items
        if "───" in model_text:
            return

        service_map = {
            'Gemini': 'gemini',
            'Mistral': 'mistral',
            'DeepSeek': 'deepseek'
        }

        if model_text.startswith('Ollama: '):
            # Extract model name from "Ollama: model_name"
            model_name = model_text.replace('Ollama: ', '')
            service = f'ollama:{model_name}'
        else:
            service = service_map.get(model_text, 'gemini')

        self.config['active_service'] = service
        save_config(self.config)
        self.model_changed.emit(service)



    def update_status(self, status, color="#4CAF50"):
        # Status updates are silent now for better performance
        pass

    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        # Keep stealth but allow activation for taskbar
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing but allow interaction
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

class AIResponseWindow(QWidget):
    """Separate popup window for AI responses with dynamic sizing"""
    def __init__(self, control_panel):
        super().__init__()
        self.control_panel = control_panel
        self.full_result_text = ""
        self.scroll_offset = 0
        self.thinking_timer = None
        self.thinking_dots = 0
        self.is_thinking = False
        self.init_ui()
        self.apply_stealth_window_style()

    def init_ui(self):
        # Set window properties
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)

        # Create main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # Result display area - clear and foggy like taskbar
        self.result_label = QLabel()
        self.result_label.setStyleSheet("""
            background: rgba(0,0,0,0.75);
            color: #fff;
            font-size: 12pt;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
            font-weight: 500;
        """)
        self.result_label.setWordWrap(True)
        layout.addWidget(self.result_label)

        # Close button with better icon
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,255,255,0.1);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 12px;
                font-size: 14pt;
                font-weight: bold;
                width: 24px;
                height: 24px;
            }
            QPushButton:hover {
                background: rgba(255,0,0,0.6);
                border: 1px solid rgba(255,0,0,0.8);
            }
        """)
        self.close_btn.setFixedSize(24, 24)
        self.close_btn.clicked.connect(self.hide)

        # Position close button in top-right corner
        self.close_btn.setParent(self)

        self.setLayout(layout)

    def show_result(self, text):
        """Display AI response with dynamic sizing, positioned relative to taskbar"""
        self.full_result_text = text
        self.scroll_offset = 0

        if DEBUG_MODE:
            lines_count = len(text.split('\n'))
            print(f"[DEBUG] Showing result with {lines_count} lines")

        # Remove any fixed size constraints from thinking mode
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)  # Qt's QWIDGETSIZE_MAX

        # Calculate optimal window size
        font_metrics = self.result_label.fontMetrics()
        lines = text.split('\n')

        # Calculate width based on longest line with better scaling
        max_line_width = max([font_metrics.horizontalAdvance(line) for line in lines] + [350])
        window_width = min(max_line_width + 80, 900)  # Increased max width and padding

        # Calculate height based on number of lines with better scaling
        line_height = font_metrics.lineSpacing()
        total_lines = len(lines)

        # Dynamic height calculation - more responsive to content
        if total_lines <= 5:
            display_lines = total_lines + 1
        elif total_lines <= 15:
            display_lines = min(total_lines + 2, 15)
        else:
            display_lines = min(total_lines, 25)  # Max 25 lines

        window_height = min(display_lines * line_height + 80, 700)  # Increased max height and padding

        # Position relative to the taskbar with strict safety checks
        screen = QApplication.primaryScreen().geometry()
        taskbar_x = max(0, self.control_panel.x())
        taskbar_y = max(0, self.control_panel.y())
        taskbar_height = self.control_panel.height()

        # Position below the taskbar, aligned to its left edge
        x = taskbar_x
        y = taskbar_y + taskbar_height + 10  # 10px gap below taskbar

        # Strict boundary validation with margins
        x = max(10, min(x, screen.width() - window_width - 10))
        y = max(10, min(y, screen.height() - window_height - 10))

        # If positioning below taskbar would go off-screen, position above instead
        if y + window_height > screen.height() - 20:
            y = max(10, taskbar_y - window_height - 10)

        # Final validation before setting geometry
        if (x >= 0 and y >= 0 and
            window_width > 0 and window_height > 0 and
            x + window_width <= screen.width() and
            y + window_height <= screen.height()):

            try:
                # Ensure no size constraints before setting geometry
                self.resize(window_width, window_height)
                self.move(x, y)
            except Exception:
                # Fallback to safe center position
                safe_x = (screen.width() - window_width) // 2
                safe_y = (screen.height() - window_height) // 2
                self.resize(window_width, window_height)
                self.move(safe_x, safe_y)
        else:
            # Use safe center position
            safe_x = max(10, (screen.width() - window_width) // 2)
            safe_y = max(10, (screen.height() - window_height) // 2)
            self.resize(window_width, window_height)
            self.move(safe_x, safe_y)

        # Position close button
        self.close_btn.move(window_width - 34, 10)

        # Update displayed text
        self._update_displayed_text()

        # Show the window
        self.show()
        self.raise_()
        self.activateWindow()

        if DEBUG_MODE:
            print(f"[DEBUG] Result window shown at ({x}, {y}) with size ({window_width}, {window_height})")
            print(f"[DEBUG] Scrolling available: {total_lines > display_lines}")

        # Auto-hide after 60 seconds
        QTimer.singleShot(60000, self.hide)

    def show_thinking(self):
        """Show thinking animation while AI processes"""
        self.is_thinking = True
        self.thinking_dots = 0

        # Force complete reset of window constraints
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)

        # Force reset window to thinking size (always small and consistent)
        thinking_width = 300
        thinking_height = 120

        # Position and size for thinking screen with safe geometry
        screen = QApplication.primaryScreen().geometry()
        taskbar_x = max(0, self.control_panel.x())
        taskbar_y = max(0, self.control_panel.y())
        taskbar_height = self.control_panel.height()

        # Position below taskbar with safe bounds
        x = taskbar_x
        y = taskbar_y + taskbar_height + 10

        # Strict boundary validation
        x = max(10, min(x, screen.width() - thinking_width - 10))
        y = max(10, min(y, screen.height() - thinking_height - 10))

        # Force the window to exact thinking size
        try:
            self.resize(thinking_width, thinking_height)
            self.move(x, y)
            # Set fixed size AFTER positioning to prevent Qt from overriding
            self.setFixedSize(thinking_width, thinking_height)
        except Exception:
            # Fallback to safe position with fixed size
            self.resize(thinking_width, thinking_height)
            self.move(100, 100)
            self.setFixedSize(thinking_width, thinking_height)

        # Position close button safely
        try:
            self.close_btn.move(thinking_width - 34, 10)
        except Exception:
            pass

        # Start thinking animation
        self._update_thinking_text()
        self._start_thinking_animation()

        # Show the window safely
        try:
            self.show()
            self.raise_()
            self.activateWindow()
        except Exception:
            pass

    def _start_thinking_animation(self):
        """Start the thinking animation timer"""
        if self.thinking_timer:
            self.thinking_timer.stop()

        self.thinking_timer = QTimer()
        self.thinking_timer.timeout.connect(self._update_thinking_text)
        self.thinking_timer.start(500)  # Update every 500ms

    def _update_thinking_text(self):
        """Update the thinking animation text"""
        if not self.is_thinking:
            return

        dots = "." * (self.thinking_dots % 4)  # 0-3 dots
        thinking_text = f"Thinking{dots}"

        # Center the text
        self.result_label.setText(f"<div style='text-align: center; padding: 20px;'>"
                                f"<h3 style='color: #4CAF50; margin-bottom: 10px;'>◉ Processing</h3>"
                                f"<p style='font-size: 14pt; color: #fff;'>{thinking_text}</p>"
                                f"</div>")

        self.thinking_dots += 1



    def stop_thinking(self):
        """Stop the thinking animation"""
        self.is_thinking = False
        if self.thinking_timer:
            self.thinking_timer.stop()
            self.thinking_timer = None

    def hideEvent(self, event):
        """Override hide event to preserve thinking state"""
        if self.is_thinking:
            print("[DEBUG] Thinking window hidden but animation continues in background")
            # Thinking animation continues even when window is hidden
        super().hideEvent(event)

    def showEvent(self, event):
        """Override show event to restore thinking state"""
        if self.is_thinking:
            print("[DEBUG] Thinking window shown, animation should be visible again")
            # Make sure animation is still running and visible
            if not self.thinking_timer or not self.thinking_timer.isActive():
                print("[DEBUG] Restarting thinking animation")
                self._start_thinking_animation()
        super().showEvent(event)

    def _update_displayed_text(self):
        """Update the displayed text based on scroll offset"""
        if not self.full_result_text:
            return

        # Use consistent height calculation with scroll_result method
        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()
        available_height = self.height() - 100  # Same calculation as scroll_result
        lines_per_view = max(3, available_height // line_height)  # Minimum 3 lines

        lines = self.full_result_text.split('\n')
        total_lines = len(lines)

        # Ensure scroll offset is within bounds - consistent with scroll_result
        max_offset = max(0, total_lines - lines_per_view)
        self.scroll_offset = max(0, min(self.scroll_offset, max_offset))

        # Get visible portion
        end_line = min(self.scroll_offset + lines_per_view, total_lines)
        visible_lines = lines[self.scroll_offset:end_line]
        visible_text = '\n'.join(visible_lines)

        # Add scroll indicators with better formatting
        scroll_info = ""
        if self.scroll_offset > 0:
            scroll_info = "<div style='text-align: center; color: #4CAF50; font-weight: bold; margin-bottom: 10px;'>▲ Scroll up (Ctrl+↑) for more ▲</div>"

        if end_line < total_lines:
            scroll_info += "<div style='text-align: center; color: #4CAF50; font-weight: bold; margin-top: 10px;'>▼ Scroll down (Ctrl+↓) for more ▼</div>"

        # Format the text with HTML for better display
        formatted_text = visible_text.replace('\n', '<br>')

        if scroll_info:
            if self.scroll_offset > 0 and end_line < total_lines:
                # Both indicators
                display_text = scroll_info.split('</div>')[0] + '</div>' + formatted_text + scroll_info.split('</div>')[1]
            elif self.scroll_offset > 0:
                # Top indicator only
                display_text = scroll_info + formatted_text
            else:
                # Bottom indicator only
                display_text = formatted_text + scroll_info
        else:
            display_text = formatted_text

        self.result_label.setText(display_text)

    def scroll_result(self, direction):
        """Scroll through the AI response"""
        # Add debug output to help diagnose issues
        if DEBUG_MODE:
            print(f"[DEBUG] Scroll attempt: direction={direction}, visible={self.isVisible()}, thinking={self.is_thinking}, has_text={bool(self.full_result_text)}")

        if not self.full_result_text or not self.isVisible() or self.is_thinking:
            if DEBUG_MODE:
                print(f"[DEBUG] Scroll blocked - text:{bool(self.full_result_text)}, visible:{self.isVisible()}, thinking:{self.is_thinking}")
            return

        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()
        available_height = self.height() - 100  # Consistent with _update_displayed_text
        lines_per_view = max(3, available_height // line_height)  # Minimum 3 lines, consistent

        lines = self.full_result_text.split('\n')
        total_lines = len(lines)

        # Update scroll offset with better bounds checking
        old_offset = self.scroll_offset
        scroll_amount = 3  # Scroll 3 lines at a time for better control
        self.scroll_offset += direction * scroll_amount

        # Ensure we don't scroll past the beginning or end - consistent calculation
        max_offset = max(0, total_lines - lines_per_view)
        self.scroll_offset = max(0, min(self.scroll_offset, max_offset))

        if DEBUG_MODE:
            print(f"[DEBUG] Scroll: old_offset={old_offset}, new_offset={self.scroll_offset}, max_offset={max_offset}, total_lines={total_lines}, lines_per_view={lines_per_view}")

        # Only update if offset actually changed
        if self.scroll_offset != old_offset:
            self._update_displayed_text()
            if DEBUG_MODE:
                print(f"[DEBUG] Scroll updated display")
        else:
            if DEBUG_MODE:
                print(f"[DEBUG] Scroll offset unchanged, no update needed")

    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        # Modified stealth style - remove WS_EX_NOACTIVATE to allow keyboard input
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing but allow input
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

class StealthScreenshotWindow(QWidget):
    """Main invisible window that handles screenshots and coordinates other windows"""
    result_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.system_prompt = load_system_prompt()
        self._init_ai_service()

        # Make this window completely invisible - it's just for coordination
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)
        self.setGeometry(-1000, -1000, 1, 1)  # Move off-screen

        self.screenshot_pixmap = None
        self.ai_processing = False  # Track if AI is currently processing

        # Create the taskbar control panel
        self.control_panel = TaskbarControlPanel(self.config, self.ai_service)
        self.control_panel.model_changed.connect(self._on_model_changed)
        self.control_panel.show()

        # Create the AI response window (initially hidden)
        self.response_window = AIResponseWindow(self.control_panel)

        # Connect settings button in control panel to our settings method
        self.control_panel.settings_btn.clicked.connect(self.show_settings_dialog)

        # Connect refresh button to refresh models
        self.control_panel.refresh_btn.clicked.connect(self.refresh_models)

        # Connect result signal for thread-safe UI update
        self.result_signal.connect(self._show_ai_response)

        # Register hotkeys
        self._register_hotkeys()

        self.apply_stealth_window_style()

    def _on_model_changed(self, service):
        """Handle model change from control panel"""
        self.config['active_service'] = service
        self._init_ai_service()
        self.control_panel.update_status(f"Switched to {service.title()}", "#4CAF50")
        QTimer.singleShot(2000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))

    def _register_hotkeys(self):
        """Register global hotkeys"""
        try:
            # Screenshot hotkey
            keyboard.add_hotkey('alt+c', lambda: QTimer.singleShot(0, self.take_screenshot))

            # Settings hotkey
            keyboard.add_hotkey('alt+s', lambda: QTimer.singleShot(0, self.show_settings_dialog))

            # Toggle control panel visibility
            keyboard.add_hotkey('ctrl+\\', lambda: QTimer.singleShot(0, self.toggle_control_panel))

            # Scrolling controls for AI response window - improved with error handling
            def scroll_up():
                try:
                    if DEBUG_MODE:
                        print("[DEBUG] Ctrl+Up hotkey triggered")
                    # Use direct call instead of QTimer to ensure immediate response
                    self.response_window.scroll_result(-1)
                except Exception as e:
                    if DEBUG_MODE:
                        print(f"[ERROR] Scroll up failed: {e}")

            def scroll_down():
                try:
                    if DEBUG_MODE:
                        print("[DEBUG] Ctrl+Down hotkey triggered")
                    # Use direct call instead of QTimer to ensure immediate response
                    self.response_window.scroll_result(1)
                except Exception as e:
                    if DEBUG_MODE:
                        print(f"[ERROR] Scroll down failed: {e}")

            keyboard.add_hotkey('ctrl+up', scroll_up)
            keyboard.add_hotkey('ctrl+down', scroll_down)

            # Movement controls for control panel
            keyboard.add_hotkey('ctrl+left', lambda: self._move_control_panel(-20, 0))
            keyboard.add_hotkey('ctrl+right', lambda: self._move_control_panel(20, 0))

            # Test scrolling functionality (only in debug mode)
            if DEBUG_MODE:
                def test_scrolling():
                    test_text = "\n".join([f"Line {i+1}: This is a test line to check scrolling functionality." for i in range(50)])
                    QTimer.singleShot(0, lambda: self.response_window.show_result(test_text))

                keyboard.add_hotkey('ctrl+shift+t', test_scrolling)
                print("[DEBUG] Test scrolling hotkey registered (Ctrl+Shift+T)")

            if DEBUG_MODE:
                print("[DEBUG] All hotkeys registered successfully")
        except Exception as e:
            if DEBUG_MODE:
                print(f"[ERROR] Failed to register hotkeys: {e}")

    def _move_control_panel(self, dx, dy):
        """Move the control panel and response window together with strict safety checks"""
        try:
            # Get screen geometry for boundary checking
            screen = QApplication.primaryScreen().geometry()

            # Calculate new position for control panel with safety checks
            current_x = max(0, self.control_panel.x())
            current_y = max(0, self.control_panel.y())
            new_x = current_x + dx
            new_y = current_y + dy

            # Check boundaries for control panel with margins
            panel_width = self.control_panel.width()
            panel_height = self.control_panel.height()

            # Constrain to screen boundaries with safety margins
            new_x = max(10, min(new_x, screen.width() - panel_width - 10))
            new_y = max(10, min(new_y, screen.height() - panel_height - 10))

            # Validate the new position
            if (new_x >= 0 and new_y >= 0 and
                new_x + panel_width <= screen.width() and
                new_y + panel_height <= screen.height()):

                # Only move if position actually changed and is valid
                if new_x != current_x or new_y != current_y:
                    # Calculate actual movement delta
                    actual_dx = new_x - current_x
                    actual_dy = new_y - current_y

                    print(f"[DEBUG] Moving control panel by ({actual_dx}, {actual_dy}) to ({new_x}, {new_y})")

                    # Move control panel safely
                    try:
                        self.control_panel.move(new_x, new_y)
                    except Exception as e:
                        print(f"[ERROR] Failed to move control panel: {e}")
                        return

                    # If response window is visible, move it too with boundary checks
                    if self.response_window.isVisible():
                        try:
                            response_x = max(0, self.response_window.x() + actual_dx)
                            response_y = max(0, self.response_window.y() + actual_dy)
                            response_width = self.response_window.width()
                            response_height = self.response_window.height()

                            # Constrain response window to screen boundaries with margins
                            response_x = max(10, min(response_x, screen.width() - response_width - 10))
                            response_y = max(10, min(response_y, screen.height() - response_height - 10))

                            # Validate response window position
                            if (response_x >= 0 and response_y >= 0 and
                                response_x + response_width <= screen.width() and
                                response_y + response_height <= screen.height()):

                                print(f"[DEBUG] Moving response window to ({response_x}, {response_y})")
                                self.response_window.move(response_x, response_y)
                            else:
                                print(f"[ERROR] Invalid response window position calculated: ({response_x}, {response_y})")
                        except Exception as e:
                            print(f"[ERROR] Failed to move response window: {e}")
            else:
                print(f"[ERROR] Invalid control panel position calculated: ({new_x}, {new_y})")

        except Exception as e:
            print(f"[ERROR] Movement operation failed: {e}")

    def toggle_control_panel(self):
        """Toggle all windows visibility while preserving AI thinking process in background"""
        try:
            if self.control_panel.isVisible():
                # Hide everything - control panel AND response window
                print("[DEBUG] Hiding all windows (thinking={}, processing={})".format(
                    self.response_window.is_thinking, self.ai_processing))

                try:
                    self.control_panel.hide()
                except Exception as e:
                    print(f"[ERROR] Failed to hide control panel: {e}")

                try:
                    self.response_window.hide()
                except Exception as e:
                    print(f"[ERROR] Failed to hide response window: {e}")

                # AI processing continues in background even though windows are hidden
                if self.ai_processing or self.response_window.is_thinking:
                    print("[DEBUG] AI process continues in background while windows hidden")
            else:
                # Show control panel with safety checks
                try:
                    # Validate control panel position before showing
                    screen = QApplication.primaryScreen().geometry()
                    panel_x = max(10, min(self.control_panel.x(), screen.width() - self.control_panel.width() - 10))
                    panel_y = max(10, min(self.control_panel.y(), screen.height() - self.control_panel.height() - 10))

                    # Ensure valid position
                    if panel_x != self.control_panel.x() or panel_y != self.control_panel.y():
                        print(f"[DEBUG] Correcting control panel position to ({panel_x}, {panel_y})")
                        self.control_panel.move(panel_x, panel_y)

                    self.control_panel.show()
                    self.control_panel.raise_()
                    self.control_panel.activateWindow()
                except Exception as e:
                    print(f"[ERROR] Failed to show control panel: {e}")

                # Show response window if there's something to show
                try:
                    if self.response_window.is_thinking:
                        # Still thinking - show thinking animation
                        print("[DEBUG] Restoring thinking window")
                        self.response_window.show()
                        self.response_window.raise_()
                    elif self.response_window.full_result_text:
                        # Has result - show the result
                        print("[DEBUG] Restoring result window")
                        self.response_window.show()
                        self.response_window.raise_()
                except Exception as e:
                    print(f"[ERROR] Failed to show response window: {e}")

                print("[DEBUG] All windows restored")

        except Exception as e:
            print(f"[ERROR] Toggle operation failed: {e}")

    def refresh_models(self):
        """Refresh the available models in the dropdown"""
        current_selection = self.control_panel.model_combo.currentText()
        self.control_panel._populate_models()

        # Try to restore previous selection
        index = self.control_panel.model_combo.findText(current_selection)
        if index >= 0:
            self.control_panel.model_combo.setCurrentIndex(index)



    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        # Main coordinator window stays fully stealth
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED | WS_EX_NOACTIVATE
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

    def take_screenshot(self):
        self.control_panel.update_status("Taking screenshot...", "#FF9800")

        # Set AI processing flag
        self.ai_processing = True

        # Show thinking screen immediately
        self.response_window.show_thinking()

        # Use QApplication.primaryScreen for faster screenshot
        screen = QApplication.primaryScreen()
        qpix = screen.grabWindow(0)
        qimg = qpix.toImage().convertToFormat(QImage.Format_RGBA8888)
        width, height = qimg.width(), qimg.height()
        ptr = qimg.bits()
        ptr.setsize(qimg.byteCount())
        arr = np.array(ptr).reshape(height, width, 4)
        from PIL import Image
        img = Image.fromarray(arr, 'RGBA')

        self.control_panel.update_status("Processing with AI...", "#2196F3")

        # Process in a separate thread to keep UI responsive
        import threading
        threading.Thread(target=self.send_screenshot_to_llm, args=(img,), daemon=True).start()

    def send_screenshot_to_llm(self, img):
        try:
            # Preprocess image for better OCR
            processed_img = preprocess_image_for_ocr(img)

            # Run OCR on preprocessed image
            ocr_text = pytesseract.image_to_string(processed_img)
        except Exception as e:
            error_text = f"Error: OCR failed - {str(e)}"
            self.control_panel.update_status("OCR failed", "#F44336")
            self.result_signal.emit(error_text)
            return

        # Prepare image bytes for AI service
        buf = io.BytesIO()
        img.save(buf, format='PNG')
        image_bytes = buf.getvalue()
        prompt = f"[OCR TEXT]\n{ocr_text.strip()}\n---\n{self.system_prompt}"

        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            service = self.config.get('active_service', 'gemini')
            self._init_ai_service()

            result = loop.run_until_complete(
                self.ai_service.send_message(service, prompt, image_bytes=image_bytes)
            )
            loop.close()

            if not result:
                result = f"No response from {service}."
                self.control_panel.update_status("No AI response", "#FF9800")
            else:
                self.control_panel.update_status("Response ready", "#4CAF50")
            self.result_signal.emit(result)
        except Exception as e:
            error_text = f"Error: LLM request failed - {str(e)}"
            self.control_panel.update_status("AI request failed", "#F44336")
            self.result_signal.emit(error_text)

    @pyqtSlot(str)
    def _show_ai_response(self, text):
        """Show AI response in the popup window"""
        # Clear AI processing flag
        self.ai_processing = False

        # Stop thinking animation
        self.response_window.stop_thinking()

        # Always prepare the result (even if windows are hidden)
        self.response_window.show_result(text)

        # If control panel is currently visible, show the response
        if self.control_panel.isVisible():
            self.response_window.show()
            self.response_window.raise_()

        # Reset status after showing result
        QTimer.singleShot(3000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))

    def show_settings_dialog(self):
        class SettingsDialog(QDialog):
            def __init__(self, config, parent=None):
                super().__init__(parent)
                self.setWindowTitle("API Configuration")
                self.setModal(True)
                self.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint | Qt.WindowCloseButtonHint)
                self.setAttribute(Qt.WA_DeleteOnClose, False)
                self.setStyleSheet("""
                    QDialog {
                        background: rgba(20,20,20,0.98);
                        color: #fff;
                        border-radius: 8px;
                        border: 2px solid rgba(255,255,255,0.3);
                    }
                    QLabel {
                        color: #fff;
                        font-size: 11pt;
                        padding: 3px;
                    }
                    QLineEdit {
                        background: rgba(0,0,0,0.8);
                        color: #fff;
                        border: 1px solid rgba(255,255,255,0.2);
                        border-radius: 4px;
                        padding: 8px;
                        font-size: 10pt;
                    }
                    QLineEdit:focus {
                        border: 1px solid rgba(76,175,80,0.5);
                    }
                    QComboBox {
                        background: rgba(0,0,0,0.8);
                        color: #fff;
                        border: 1px solid rgba(255,255,255,0.2);
                        border-radius: 4px;
                        padding: 6px;
                        font-size: 10pt;
                    }
                    QPushButton {
                        background: rgba(76,175,80,0.8);
                        color: #fff;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 16px;
                        font-size: 11pt;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background: rgba(76,175,80,1.0);
                    }
                    QPushButton#show_btn {
                        background: rgba(33,150,243,0.8);
                        padding: 4px 8px;
                        font-size: 9pt;
                    }
                    QPushButton#show_btn:hover {
                        background: rgba(33,150,243,1.0);
                    }
                """)
                layout = QVBoxLayout()
                layout.setSpacing(12)
                layout.setContentsMargins(20, 20, 20, 20)

                # Title with icon
                title = QLabel("🔑 API Configuration")
                title.setStyleSheet("font-size: 14pt; font-weight: bold; color: #4CAF50; padding: 8px;")
                layout.addWidget(title)

                # Service selection
                layout.addWidget(QLabel("Active AI Service:"))
                self.service_combo = QComboBox()
                self.service_combo.addItems(["gemini", "mistral", "deepseek"])
                self.service_combo.setCurrentText(config.get('active_service', 'gemini'))
                layout.addWidget(self.service_combo)

                # API key fields with show/hide functionality
                def create_api_key_field(label_text, config_key, placeholder):
                    layout.addWidget(QLabel(label_text))

                    # Container for input and show button
                    container = QWidget()
                    container_layout = QHBoxLayout()
                    container_layout.setContentsMargins(0, 0, 0, 0)
                    container_layout.setSpacing(5)

                    # Input field
                    entry = QLineEdit()
                    entry.setPlaceholderText(placeholder)
                    api_key = config.get(config_key, '')
                    if api_key:
                        # Show masked version if key exists
                        entry.setText('*' * min(len(api_key), 20) + api_key[-4:] if len(api_key) > 4 else '*' * len(api_key))
                        entry.setProperty('actual_value', api_key)  # Store actual value
                        entry.setProperty('is_masked', True)
                    else:
                        entry.setProperty('actual_value', '')
                        entry.setProperty('is_masked', False)
                    entry.setEchoMode(QLineEdit.Password)

                    # Show/Hide button
                    show_btn = QPushButton("Show")
                    show_btn.setObjectName("show_btn")
                    show_btn.setFixedWidth(50)

                    def toggle_visibility():
                        if entry.property('is_masked') and entry.property('actual_value'):
                            # Show actual value
                            entry.setText(entry.property('actual_value'))
                            entry.setEchoMode(QLineEdit.Normal)
                            show_btn.setText("Hide")
                            entry.setProperty('is_masked', False)
                        else:
                            # Hide value
                            actual_val = entry.text() if not entry.property('is_masked') else entry.property('actual_value')
                            if actual_val:
                                entry.setText('*' * min(len(actual_val), 20) + actual_val[-4:] if len(actual_val) > 4 else '*' * len(actual_val))
                                entry.setProperty('actual_value', actual_val)
                                entry.setProperty('is_masked', True)
                                entry.setEchoMode(QLineEdit.Password)
                                show_btn.setText("Show")

                    show_btn.clicked.connect(toggle_visibility)

                    container_layout.addWidget(entry)
                    container_layout.addWidget(show_btn)
                    container.setLayout(container_layout)
                    layout.addWidget(container)

                    return entry

                # Create API key fields
                self.gemini_entry = create_api_key_field("Gemini API Key:", 'gemini_api_key', "Enter your Gemini API key...")
                self.mistral_entry = create_api_key_field("Mistral API Key:", 'mistral_api_key', "Enter your Mistral API key...")
                self.deepseek_entry = create_api_key_field("OpenRouter API Key (DeepSeek):", 'openrouter_api_key', "Enter your OpenRouter API key...")

                # Save button
                save_btn = QPushButton("Save Configuration")
                save_btn.clicked.connect(self.accept)
                layout.addWidget(save_btn)

                self.setLayout(layout)

                # Adaptive sizing based on screen size
                screen = QApplication.primaryScreen().geometry()
                dialog_width = min(500, screen.width() - 100)
                dialog_height = min(450, screen.height() - 100)
                self.setFixedSize(dialog_width, dialog_height)

                # Center the dialog on screen with boundary checks
                x = max(0, (screen.width() - dialog_width) // 2)
                y = max(0, (screen.height() - dialog_height) // 2)
                self.move(x, y)

            def get_config(self):
                def get_actual_value(entry):
                    if entry.property('is_masked') and entry.property('actual_value'):
                        return entry.property('actual_value')
                    return entry.text().strip()

                return {
                    'active_service': self.service_combo.currentText(),
                    'gemini_api_key': get_actual_value(self.gemini_entry),
                    'mistral_api_key': get_actual_value(self.mistral_entry),
                    'openrouter_api_key': get_actual_value(self.deepseek_entry),
                    'mistral_model': 'mistral-small'  # Keep default
                }

        dlg = SettingsDialog(self.config, self.control_panel)
        if dlg.exec_() == QDialog.Accepted:
            old_service = self.config.get('active_service', 'gemini')
            self.config.update(dlg.get_config())
            save_config(self.config)
            self._init_ai_service()

            # Update control panel
            new_service = self.config.get('active_service', 'gemini')
            service_display = {'gemini': 'Gemini', 'mistral': 'Mistral', 'deepseek': 'DeepSeek'}
            self.control_panel.model_combo.setCurrentText(service_display.get(new_service, 'Gemini'))

            if old_service != new_service:
                self.control_panel.update_status(f"Switched to {new_service.title()}", "#4CAF50")
                QTimer.singleShot(3000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))
            else:
                self.control_panel.update_status("Settings saved", "#4CAF50")
                QTimer.singleShot(2000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))

    def _init_ai_service(self):
        # Always initialize the correct service
        self.ai_service = AIService(mistral_model=self.config.get('mistral_model', 'mistral-small'))
        service = self.config.get('active_service', 'gemini')
        if service == 'gemini' and self.config.get('gemini_api_key'):
            self.ai_service.initialize_gemini(self.config['gemini_api_key'])
        elif service == 'mistral' and self.config.get('mistral_api_key'):
            self.ai_service.initialize_mistral(self.config['mistral_api_key'])
        elif service == 'deepseek' and self.config.get('openrouter_api_key'):
            self.ai_service.initialize_deepseek(self.config['openrouter_api_key'])
        elif service.startswith('ollama:'):
            self.ai_service.initialize_ollama()

def preprocess_image_for_ocr(image):
    """Preprocess image using OpenCV to improve OCR accuracy"""
    try:
        # Convert PIL image to OpenCV format
        img_array = np.array(image)
        if len(img_array.shape) == 4:  # RGBA
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
        elif len(img_array.shape) == 3:  # RGB
            pass
        else:  # Already grayscale
            img_array = cv2.cvtColor(img_array, cv2.COLOR_GRAY2RGB)

        # Convert to grayscale
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

        # Apply multiple preprocessing techniques for better OCR

        # 1. Noise reduction
        denoised = cv2.medianBlur(gray, 3)

        # 2. Contrast enhancement using CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # 3. Adaptive thresholding for better text extraction
        thresh = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # 4. Morphological operations to clean up text
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # 5. Scale up image for better OCR (2x)
        height, width = cleaned.shape
        scaled = cv2.resize(cleaned, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)

        # Convert back to PIL Image
        from PIL import Image
        processed_image = Image.fromarray(scaled)

        return processed_image

    except Exception as e:
        # If preprocessing fails, return original image
        return image

def prompt_for_api_key_if_missing(config):
    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton
    service = config.get('active_service', 'gemini')
    key_map = {
        'gemini': ('gemini_api_key', 'Gemini API Key'),
        'mistral': ('mistral_api_key', 'Mistral API Key'),
        'deepseek': ('openrouter_api_key', 'OpenRouter API Key (DeepSeek)')
    }
    key_name, label = key_map.get(service, (None, None))
    if not key_name:
        return config
    if config.get(key_name):
        return config
    # Prompt user for API key
    class ApiKeyDialog(QDialog):
        def __init__(self, label, parent=None):
            super().__init__(parent)
            self.setWindowTitle("API Key Required")
            self.setModal(True)
            layout = QVBoxLayout()
            layout.addWidget(QLabel(f"Enter your {label}:"))
            self.entry = QLineEdit()
            self.entry.setEchoMode(QLineEdit.Password)
            layout.addWidget(self.entry)
            btn = QPushButton("Save")
            btn.clicked.connect(self.accept)
            layout.addWidget(btn)
            self.setLayout(layout)
        def get_key(self):
            return self.entry.text().strip()
    dlg = ApiKeyDialog(label)
    if dlg.exec_() == QDialog.Accepted:
        config[key_name] = dlg.get_key()
        save_config(config)
    return config

def start_app():
    app = QApplication(sys.argv)
    config = load_config()
    config = prompt_for_api_key_if_missing(config)

    # Create the main stealth window (invisible coordinator)
    window = StealthScreenshotWindow()
    window.config = config

    sys.exit(app.exec_())

# Add a global exception hook to log uncaught exceptions and show a user-friendly error message
import traceback
from PyQt5.QtWidgets import QMessageBox

def exception_hook(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    print(f"[CRITICAL] Uncaught exception:\n{error_msg}")
    try:
        QMessageBox.critical(None, "Application Error", f"A critical error occurred:\n{value}\n\nSee console for details.")
    except Exception:
        pass
    sys.__excepthook__(exctype, value, tb)

sys.excepthook = exception_hook

if __name__ == '__main__':
    start_app()